/**
 * Contract Awards Dashboard using Centralized Configuration
 * Demonstrates the new centralized approach using dashboard_metadata.json
 */

import React from 'react';
import { CentralizedDashboard } from '../../../components/dashboard/CentralizedDashboard';
import { CustomBarChart } from '../../../components/charts/CustomBarChart';
import { CustomLineChart } from '../../../components/charts/CustomLineChart';
import { CustomPieChart } from '../../../components/charts/CustomPieChart';
import { KPIWidget } from '../../../components/widgets/KPIWidget/KPIWidget';

// Contract Awards Dashboard ID from dashboard_metadata.json
const CONTRACT_AWARDS_DASHBOARD_ID = '68654950099a11833ea60935';

interface ContractAwardsDashboardCentralizedProps {
  useCustomCharts?: boolean;
}

export const ContractAwardsDashboardCentralized: React.FC<ContractAwardsDashboardCentralizedProps> = ({
  useCustomCharts = false,
}) => {
  // Mock data for custom charts
  const mockData = {
    monthlyValue: {
      value: 485.7,
      trend: 15.3,
    },
    stateData: [
      { state: 'CA', value: 52.3 },
      { state: 'TX', value: 48.7 },
      { state: 'FL', value: 41.2 },
      { state: 'NY', value: 38.9 },
      { state: 'IL', value: 35.4 },
    ],
    ytdTrend: [
      { month: 'Jan', value: 425.2 },
      { month: 'Feb', value: 438.6 },
      { month: 'Mar', value: 452.1 },
      { month: 'Apr', value: 468.3 },
      { month: 'May', value: 475.9 },
      { month: 'Jun', value: 485.7 },
    ],
    modeBreakdown: [
      { name: 'Highway & Pavement', value: 45.2, color: '#0088FE' },
      { name: 'Bridge & Tunnel', value: 28.7, color: '#00C49F' },
      { name: 'Airport', value: 15.3, color: '#FFBB28' },
      { name: 'Transit', value: 10.8, color: '#FF8042' },
    ],
  };

  const customWidgetRenderer = (widgetId: string, position: any, title?: string) => {
    if (!useCustomCharts) {
      return null; // Use default Sisense widgets
    }

    // Map actual widget IDs from dashboard_metadata.json to custom components
    switch (widgetId) {
      case '68654950099a11833ea60936': // Monthly Value KPI
        return (
          <KPIWidget
            key={widgetId}
            title="Monthly Value"
            value={`$${mockData.monthlyValue.value}B`}
            trend={mockData.monthlyValue.trend}
            position={position}
          />
        );

      case '68654950099a11833ea6093a': // Monthly Value by State
        return (
          <CustomBarChart
            key={widgetId}
            data={mockData.stateData}
            xKey="state"
            yKey="value"
            title="Monthly Value by State"
            position={position}
            color="#82ca9d"
          />
        );

      case '68654950099a11833ea6093b': // YTD Value
        return (
          <CustomLineChart
            key={widgetId}
            data={mockData.ytdTrend}
            xKey="month"
            yKeys={['value']}
            title="YTD Value"
            position={position}
            colors={['#8884d8']}
          />
        );

      case '68654950099a11833ea6093c': // YTD Value by Mode
        return (
          <CustomPieChart
            key={widgetId}
            data={mockData.modeBreakdown}
            title="YTD Value by Mode"
            position={position}
          />
        );

      case '68654950099a11833ea6093d': // Monthly Number of Contract Awards
        return (
          <CustomBarChart
            key={widgetId}
            data={mockData.stateData}
            xKey="state"
            yKey="value"
            title="Monthly Number of Contract Awards"
            position={position}
            color="#ff7300"
          />
        );

      default:
        return null; // Use default Sisense widget
    }
  };

  return (
    <CentralizedDashboard
      dashboardId={CONTRACT_AWARDS_DASHBOARD_ID}
      customWidgetRenderer={useCustomCharts ? customWidgetRenderer : undefined}
    />
  );
};

// Export variation for backward compatibility
export const ContractAwardsDashboardCentralizedWithCustomCharts: React.FC = () => (
  <ContractAwardsDashboardCentralized useCustomCharts={true} />
);
