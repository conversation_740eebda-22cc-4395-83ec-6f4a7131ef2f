import type { DashboardLayoutConfig } from '../types/dashboardMetadata.types';
import type { DashboardConfig } from '../types/dashboard.types';
import { DashboardMetadataService } from './dashboardMetadata.service';

/**
 * Centralized dashboard configuration service
 * Provides layout configurations and dashboard configs based on dashboard_metadata.json
 */
export class DashboardConfigurationService {
  
  /**
   * Layout configurations for each dashboard
   * This defines positioning, custom titles, and visibility for widgets
   */
  private static layoutConfigs: DashboardLayoutConfig = {
    // Contract Awards Dashboard
    '68654950099a11833ea60935': {
      title: 'Contract Awards',
      description: 'State & Local Government Contract Awards with TTM and YTD metrics, geographic distribution, and trending analysis',
      path: '/dashboard/contract-awards',
      icon: '📄',
      widgetLayout: {
        // Top KPI Row - Always Visible
        '68654950099a11833ea60936': {
          position: { x: 0, y: 0, w: 3, h: 2 },
          customTitle: 'Monthly Value',
          visible: true,
        },
        '68654950099a11833ea60937': {
          position: { x: 3, y: 0, w: 3, h: 2 },
          customTitle: 'YTD Value',
          visible: true,
        },
        // Monthly Value by State Map
        '68654950099a11833ea6093a': {
          position: { x: 0, y: 2, w: 6, h: 4 },
          customTitle: 'Monthly Value by State',
          visible: true,
          tab: 'monthly',
        },
        // YTD Value Chart
        '68654950099a11833ea6093b': {
          position: { x: 6, y: 2, w: 6, h: 4 },
          customTitle: 'YTD Value',
          visible: true,
          tab: 'ytd',
        },
        // TTM Value Chart
        '68654950099a11833ea60945': {
          position: { x: 0, y: 6, w: 6, h: 4 },
          customTitle: 'TTM Value',
          visible: true,
          tab: 'ttm',
        },
        // YTD Value by Mode Pie Chart
        '68654950099a11833ea6093c': {
          position: { x: 6, y: 6, w: 6, h: 4 },
          customTitle: 'YTD Value by Mode',
          visible: true,
          tab: 'ytd',
        },
        // Monthly Number of Contract Awards
        '68654950099a11833ea6093d': {
          position: { x: 0, y: 10, w: 6, h: 4 },
          customTitle: 'Monthly Number of Contract Awards',
          visible: true,
          tab: 'monthly',
        },
        // TTM Value by Mode
        '68654950099a11833ea6094f': {
          position: { x: 6, y: 10, w: 6, h: 4 },
          customTitle: 'TTM Value by Mode',
          visible: true,
          tab: 'ttm',
        },
        // Rich text widgets - will show "Coming Soon" automatically
        '68654950099a11833ea60938': {
          position: { x: 0, y: 14, w: 12, h: 2 },
          customTitle: 'Dashboard Information',
          visible: true
        },
        '68654950099a11833ea60951': { position: { x: 0, y: 0, w: 0, h: 0 }, visible: false }, // Rich text
        '68654950099a11833ea60952': { position: { x: 0, y: 0, w: 0, h: 0 }, visible: false }, // Rich text
      },
    },

    // Summary Dashboard
    '6865541f099a11833ea60aef': {
      title: 'Summary Dashboard',
      description: 'Executive overview of key transportation construction metrics',
      path: '/dashboard/summary',
      icon: '📊',
      widgetLayout: {
        // TTM U.S. Transp. Const. Work
        '6865541f099a11833ea60af4': {
          position: { x: 0, y: 0, w: 6, h: 4 },
          customTitle: 'TTM U.S. Transportation Construction Work',
          visible: true,
        },
        // ARTBA 2025 Transp. Const. Market Outlook
        '6865541f099a11833ea60af6': {
          position: { x: 6, y: 0, w: 6, h: 4 },
          customTitle: 'ARTBA 2025 Transportation Construction Market Outlook',
          visible: true,
        },
        // U.S. Transp. Const. Work by Mode
        '6865541f099a11833ea60af7': {
          position: { x: 0, y: 4, w: 6, h: 4 },
          customTitle: 'U.S. Transportation Construction Work by Mode',
          visible: true,
        },
        // TTM State & Local Govt. Transp. Contract Awards
        '6865541f099a11833ea60b03': {
          position: { x: 6, y: 4, w: 6, h: 4 },
          customTitle: 'TTM State & Local Government Transportation Contract Awards',
          visible: true,
        },
      },
    },

    // Value Put in Place Dashboard
    '68655384099a11833ea60aa4': {
      title: 'Value Put in Place',
      description: 'Construction value put in place analysis with monthly, YTD, and TTM breakdowns',
      path: '/dashboard/value-put-in-place',
      icon: '🏗️',
      widgetLayout: {
        // Monthly Value
        '68655384099a11833ea60aab': {
          position: { x: 0, y: 0, w: 6, h: 4 },
          customTitle: 'Monthly Value',
          visible: true,
        },
        // YTD Value
        '68655384099a11833ea60aac': {
          position: { x: 6, y: 0, w: 6, h: 4 },
          customTitle: 'YTD Value',
          visible: true,
        },
        // TTM Value
        '68655384099a11833ea60ab2': {
          position: { x: 0, y: 4, w: 6, h: 4 },
          customTitle: 'TTM Value',
          visible: true,
        },
        // Monthly Value Breakdown by Mode
        '68655384099a11833ea60aad': {
          position: { x: 6, y: 4, w: 6, h: 4 },
          customTitle: 'Monthly Value Breakdown by Mode',
          visible: true,
        },
      },
    },
  };

  /**
   * Get dashboard configuration by ID
   */
  static getDashboardConfig(dashboardId: string): DashboardConfig | undefined {
    const layoutConfig = this.layoutConfigs[dashboardId];
    return DashboardMetadataService.convertToDashboardConfig(dashboardId, layoutConfig);
  }

  /**
   * Get all available dashboard configurations
   */
  static getAllDashboardConfigs(): DashboardConfig[] {
    const configs: DashboardConfig[] = [];
    
    // Get configured dashboards first
    for (const dashboardId of Object.keys(this.layoutConfigs)) {
      const config = this.getDashboardConfig(dashboardId);
      if (config) {
        configs.push(config);
      }
    }

    // Add any unconfigured dashboards with default layout
    const allDashboards = DashboardMetadataService.getAllDashboards();
    for (const dashboard of allDashboards) {
      if (!this.layoutConfigs[dashboard.Dashboard_ID]) {
        const config = DashboardMetadataService.convertToDashboardConfig(dashboard.Dashboard_ID);
        if (config) {
          configs.push(config);
        }
      }
    }

    return configs;
  }

  /**
   * Get dashboard by path
   */
  static getDashboardByPath(path: string): DashboardConfig | undefined {
    const configs = this.getAllDashboardConfigs();
    return configs.find(config => config.path === path);
  }

  /**
   * Check if dashboard has custom layout configuration
   */
  static hasCustomLayout(dashboardId: string): boolean {
    return dashboardId in this.layoutConfigs;
  }

  /**
   * Get widget configuration for a specific widget
   */
  static getWidgetConfig(dashboardId: string, widgetId: string) {
    const layoutConfig = this.layoutConfigs[dashboardId];
    return layoutConfig?.widgetLayout[widgetId];
  }

  /**
   * Get dashboard summary for navigation
   */
  static getDashboardSummary() {
    return this.getAllDashboardConfigs().map(config => ({
      id: config.id,
      title: config.title,
      description: config.description,
      path: config.path,
      icon: config.icon,
      widgetCount: config.widgets.length,
    }));
  }
}
