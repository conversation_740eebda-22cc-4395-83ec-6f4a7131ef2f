import React from 'react';
import { ErrorBoundary } from '../common/ErrorBoundary';
import { DashboardLayout } from '../layout/DashboardLayout';
import { SisenseWidget } from '../widgets/SisenseWidget/SisenseWidget';
import { DashboardConfigurationService } from '../../services/dashboardConfiguration.service';
import { useSisenseStyleOverride } from '../../hooks/useSisenseStyleOverride';

interface CentralizedDashboardProps {
  dashboardId: string;
  customWidgetRenderer?: (widgetId: string, position: any, title?: string) => React.ReactNode;
}

/**
 * Centralized Dashboard Component
 * Uses dashboard_metadata.json as the single source of truth for dashboard configuration
 */
export const CentralizedDashboard: React.FC<CentralizedDashboardProps> = ({
  dashboardId,
  customWidgetRenderer,
}) => {
  // Get dashboard configuration from centralized service
  const dashboardConfig = DashboardConfigurationService.getDashboardConfig(dashboardId);
  
  // Apply persistent style overrides for Sisense widgets
  useSisenseStyleOverride();

  if (!dashboardConfig) {
    return (
      <ErrorBoundary context={`dashboard-${dashboardId}`}>
        <div style={{ padding: '20px', textAlign: 'center' }}>
          <h2>Dashboard Not Found</h2>
          <p>Dashboard with ID "{dashboardId}" could not be found in the configuration.</p>
          <p>Available dashboards:</p>
          <ul style={{ textAlign: 'left', display: 'inline-block' }}>
            {DashboardConfigurationService.getDashboardSummary().map(summary => (
              <li key={summary.id}>
                {summary.title} (ID: {summary.id})
              </li>
            ))}
          </ul>
        </div>
      </ErrorBoundary>
    );
  }

  const renderWidget = (widgetId: string, position: any, title?: string) => {
    // Use custom renderer if provided
    if (customWidgetRenderer) {
      const customWidget = customWidgetRenderer(widgetId, position, title);
      if (customWidget) {
        return customWidget;
      }
    }

    // Get widget metadata for chart type information
    const widgetInfo = DashboardConfigurationService.getWidgetConfig(dashboardConfig.id, widgetId);
    const widget = dashboardConfig.widgets.find(w => w.id === widgetId);

    // Default: render Sisense widget with chart type information
    return (
      <SisenseWidget
        key={widgetId}
        widgetId={widgetId}
        dashboardId={dashboardConfig.id}
        title={title}
        position={position}
        includeDashboardFilters={true}
        widgetType={widget?.type}
        chartType={widget?.config?.chartType}
        chartSubtype={widget?.config?.chartSubtype}
      />
    );
  };

  return (
    <ErrorBoundary context={`dashboard-${dashboardId}`}>
      <DashboardLayout
        title={dashboardConfig.title}
        description={dashboardConfig.description}
      >
        {dashboardConfig.widgets.map((widget) => 
          renderWidget(widget.id, widget.position, widget.title)
        )}
      </DashboardLayout>
    </ErrorBoundary>
  );
};

/**
 * Hook to get dashboard configuration
 */
export const useDashboardConfig = (dashboardId: string) => {
  return DashboardConfigurationService.getDashboardConfig(dashboardId);
};

/**
 * Hook to get all dashboard configurations
 */
export const useAllDashboardConfigs = () => {
  return DashboardConfigurationService.getAllDashboardConfigs();
};

/**
 * Hook to get dashboard summary for navigation
 */
export const useDashboardSummary = () => {
  return DashboardConfigurationService.getDashboardSummary();
};
