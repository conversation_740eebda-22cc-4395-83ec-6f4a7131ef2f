# Centralized Dashboard Configuration System

This document describes the new centralized dashboard configuration system that uses `dashboard_metadata.json` as the single source of truth for dashboard and widget configurations.

## Overview

The centralized configuration system eliminates hardcoded dashboard and widget IDs throughout the codebase by:

1. **Reading from `dashboard_metadata.json`** - A comprehensive metadata file containing all dashboard and widget information
2. **Providing centralized services** - Services that parse metadata and provide dashboard configurations
3. **Maintaining compatibility** - Preserving existing Sisense SDK integration and liquid glass design
4. **Enabling data-driven dashboards** - Making dashboard configuration more maintainable and flexible

## Architecture

### Core Components

1. **`DashboardMetadataService`** - Parses and provides access to dashboard metadata
2. **`DashboardConfigurationService`** - Converts metadata to dashboard configurations with layout information
3. **`CentralizedDashboard`** - React component that renders dashboards using centralized configuration
4. **`DashboardMigrationUtil`** - Utilities to help migrate from hardcoded configurations

### File Structure

```
src/
├── types/
│   └── dashboardMetadata.types.ts     # Types for metadata structure
├── services/
│   ├── dashboardMetadata.service.ts   # Core metadata service
│   └── dashboardConfiguration.service.ts # Configuration service with layouts
├── components/
│   └── dashboard/
│       └── CentralizedDashboard.tsx   # Main dashboard component
├── utils/
│   └── dashboardMigration.util.ts     # Migration utilities
└── pages/dashboards/
    └── [DashboardName]/
        └── [DashboardName]Centralized.tsx # Migrated dashboard examples
```

## Usage

### Basic Dashboard

```tsx
import { CentralizedDashboard } from '../../../components/dashboard/CentralizedDashboard';

const CONTRACT_AWARDS_DASHBOARD_ID = '68654950099a11833ea60935';

export const ContractAwardsDashboard: React.FC = () => {
  return (
    <CentralizedDashboard
      dashboardId={CONTRACT_AWARDS_DASHBOARD_ID}
    />
  );
};
```

### Dashboard with Custom Charts

```tsx
import { CentralizedDashboard } from '../../../components/dashboard/CentralizedDashboard';
import { CustomBarChart } from '../../../components/charts/CustomBarChart';

export const ContractAwardsDashboardWithCustomCharts: React.FC = () => {
  const customWidgetRenderer = (widgetId: string, position: any, title?: string) => {
    switch (widgetId) {
      case '68654950099a11833ea6093a': // Monthly Value by State
        return (
          <CustomBarChart
            key={widgetId}
            data={mockData}
            title="Monthly Value by State"
            position={position}
          />
        );
      default:
        return null; // Use default Sisense widget
    }
  };

  return (
    <CentralizedDashboard
      dashboardId="68654950099a11833ea60935"
      customWidgetRenderer={customWidgetRenderer}
    />
  );
};
```

### Using Hooks

```tsx
import { useDashboardConfig, useDashboardSummary } from '../../../components/dashboard/CentralizedDashboard';

export const DashboardSelector: React.FC = () => {
  const dashboards = useDashboardSummary();
  
  return (
    <div>
      {dashboards.map(dashboard => (
        <div key={dashboard.id}>
          <h3>{dashboard.title}</h3>
          <p>{dashboard.description}</p>
          <span>{dashboard.widgetCount} widgets</span>
        </div>
      ))}
    </div>
  );
};
```

## Configuration

### Dashboard Layout Configuration

Add layout configurations in `DashboardConfigurationService`:

```typescript
private static layoutConfigs: DashboardLayoutConfig = {
  '68654950099a11833ea60935': { // Dashboard ID from metadata
    title: 'Contract Awards',
    description: 'State & Local Government Contract Awards',
    path: '/dashboard/contract-awards',
    icon: '📄',
    widgetLayout: {
      '68654950099a11833ea60936': { // Widget ID from metadata
        position: { x: 0, y: 0, w: 3, h: 2 },
        customTitle: 'Monthly Value',
        visible: true,
      },
      // ... more widgets
    },
  },
};
```

### Widget Configuration Options

- **`position`** - Grid position and size
- **`customTitle`** - Override the title from metadata
- **`visible`** - Show/hide the widget
- **`tab`** - Assign widget to a specific tab

## Available Dashboards

The system automatically discovers dashboards from `dashboard_metadata.json`:

1. **Summary Dashboard** (`6865541f099a11833ea60aef`)
2. **Contract Awards** (`68654950099a11833ea60935`) ✅ Configured
3. **Value Put in Place** (`68655384099a11833ea60aa4`) ✅ Configured
4. **Federal-Aid Obligations** (`68655464099a11833ea60b57`)
5. **IIJA State Funding** (`6865547a099a11833ea60b8d`)
6. **State Legislative Initiatives** (`68655309099a11833ea60a2a`)
7. **How States Use Their Federal Aid Highway Funds** (`686554bc099a11833ea60be4`)
8. **State DOT Budgets** (`686554d1099a11833ea60c20`)
9. **Material Prices** (`686554e7099a11833ea60c53`)

## Migration Guide

### Step 1: Analyze Current Configuration

```typescript
import { DashboardMigrationUtil } from '../../../utils/dashboardMigration.util';

// Compare existing config with centralized version
const comparison = DashboardMigrationUtil.compareDashboardConfigs(
  existingConfig,
  '68654950099a11833ea60935'
);

console.log(comparison.differences);
console.log(comparison.suggestions);
```

### Step 2: Generate Migration Code

```typescript
const migrationCode = DashboardMigrationUtil.generateMigrationCode('68654950099a11833ea60935');
console.log(migrationCode);
```

### Step 3: Update Dashboard Component

Replace hardcoded configurations with centralized approach:

```diff
- import { contractAwardsDashboardConfig } from './contractAwardsDashboard.config';
+ import { CentralizedDashboard } from '../../../components/dashboard/CentralizedDashboard';

- const dashboardId = contractAwardsDashboardConfig.id;
+ const dashboardId = '68654950099a11833ea60935';

- <DashboardLayout title={contractAwardsDashboardConfig.title}>
-   {contractAwardsDashboardConfig.widgets.map(...)}
- </DashboardLayout>
+ <CentralizedDashboard dashboardId={dashboardId} />
```

## Benefits

1. **Single Source of Truth** - All dashboard metadata in one file
2. **Reduced Code Duplication** - No more scattered config files
3. **Easier Maintenance** - Update metadata file instead of multiple configs
4. **Better Consistency** - Standardized approach across all dashboards
5. **Improved Discoverability** - Automatic dashboard discovery
6. **Flexible Layouts** - Easy to adjust widget positioning and visibility
7. **Backward Compatibility** - Existing functionality preserved

## Validation

Use the migration utility to validate metadata integrity:

```typescript
const validation = DashboardMigrationUtil.validateMetadata();
console.log('Valid:', validation.isValid);
console.log('Errors:', validation.errors);
console.log('Warnings:', validation.warnings);
```

## Next Steps

1. **Configure remaining dashboards** - Add layout configurations for unconfigured dashboards
2. **Migrate existing dashboards** - Gradually replace hardcoded configs
3. **Update routing** - Use centralized paths for navigation
4. **Remove old config files** - Clean up after migration
5. **Add dashboard filters** - Implement centralized filter management
6. **Enhance metadata** - Add more metadata fields as needed
