import React from 'react';
import { ComingSoonWidget } from './ComingSoonWidget';
import { theme } from '../../../config/theme.config';

/**
 * Demo component to showcase the ComingSoonWidget
 */
export const ComingSoonWidgetDemo: React.FC = () => {
  return (
    <div style={{ 
      padding: '20px', 
      background: theme.colors.backgroundGradient,
      minHeight: '100vh'
    }}>
      <h1 style={{ color: theme.colors.text.primary, marginBottom: '30px' }}>
        Coming Soon Widget Demo
      </h1>
      
      <div style={{ 
        display: 'grid', 
        gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', 
        gap: '20px',
        marginBottom: '30px'
      }}>
        {/* Map Widget */}
        <ComingSoonWidget
          title="Monthly Value by State"
          position={{ x: 0, y: 0, w: 6, h: 4 }}
          widgetType="map/area"
          reason="Map widgets are not currently supported by WidgetById"
        />

        {/* BloX Widget */}
        <ComingSoonWidget
          title="Custom BloX Widget"
          position={{ x: 6, y: 0, w: 6, h: 4 }}
          widgetType="BloX"
          reason="BloX widgets require special handling and are not supported yet"
        />

        {/* Rich Text Widget */}
        <ComingSoonWidget
          title="Rich Text Editor"
          position={{ x: 0, y: 4, w: 6, h: 3 }}
          widgetType="richtexteditor"
          reason="Rich text widgets are not supported by WidgetById"
        />

        {/* Widgets Tabber */}
        <ComingSoonWidget
          title="Widget Tabs"
          position={{ x: 6, y: 4, w: 6, h: 3 }}
          widgetType="WidgetsTabber"
          reason="Tabbed widget containers are not supported by WidgetById"
        />
      </div>

      <div style={{ 
        padding: '20px', 
        background: theme.colors.surface,
        borderRadius: theme.borderRadius.lg,
        border: `1px solid ${theme.colors.border.glass}`,
        backdropFilter: `blur(${theme.blur.md})`,
      }}>
        <h2 style={{ color: theme.colors.text.primary, marginBottom: '15px' }}>
          Unsupported Widget Types
        </h2>
        <p style={{ color: theme.colors.text.secondary, marginBottom: '15px' }}>
          The following widget types automatically show the "Coming Soon" placeholder:
        </p>
        <ul style={{ color: theme.colors.text.secondary }}>
          <li><strong>map/area</strong> - Geographic area maps (like state maps)</li>
          <li><strong>map/scatter</strong> - Geographic scatter maps</li>
          <li><strong>areamap/usa</strong> - USA area maps</li>
          <li><strong>BloX</strong> - Custom BloX widgets</li>
          <li><strong>WidgetsTabber</strong> - Tabbed widget containers</li>
          <li><strong>richtexteditor</strong> - Rich text editor widgets</li>
        </ul>
        <p style={{ color: theme.colors.text.muted, fontSize: '14px', marginTop: '15px' }}>
          These widgets are automatically detected and replaced with the "Coming Soon" placeholder 
          to maintain a consistent user experience while these features are being developed.
        </p>
      </div>
    </div>
  );
};
