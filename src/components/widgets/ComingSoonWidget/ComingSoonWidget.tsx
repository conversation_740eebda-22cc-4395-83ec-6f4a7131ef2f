import React from 'react';
import { WidgetContainer } from '../../layout/WidgetContainer';
import { theme } from '../../../config/theme.config';
import type { WidgetPosition } from '../../../types/dashboard.types';

interface ComingSoonWidgetProps {
  title?: string;
  position: WidgetPosition;
  widgetType?: string;
  reason?: string;
}

export const ComingSoonWidget: React.FC<ComingSoonWidgetProps> = ({
  title,
  position,
  widgetType = 'map',
  reason = 'This widget type is not currently supported by WidgetById',
}) => {
  return (
    <WidgetContainer title={title} position={position} widgetType="chart">
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          height: '100%',
          padding: theme.spacing.xl,
          textAlign: 'center',
          background: `linear-gradient(135deg, ${theme.colors.surface}99 0%, ${theme.colors.background}99 100%)`,
          backdropFilter: `blur(${theme.blur.md})`,
          WebkitBackdropFilter: `blur(${theme.blur.md})`,
          borderRadius: theme.borderRadius.lg,
          border: `1px solid ${theme.colors.border.glass}`,
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        {/* Background decoration */}
        <div
          style={{
            position: 'absolute',
            top: '-50%',
            left: '-50%',
            width: '200%',
            height: '200%',
            background: `radial-gradient(circle, ${theme.colors.primary}10 0%, transparent 70%)`,
            animation: 'pulse 3s ease-in-out infinite',
            pointerEvents: 'none',
          }}
        />

        {/* Icon */}
        <div
          style={{
            width: '80px',
            height: '80px',
            borderRadius: '50%',
            background: `linear-gradient(135deg, ${theme.colors.warning} 0%, ${theme.colors.accent} 100%)`,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            marginBottom: theme.spacing.lg,
            boxShadow: `0 8px 32px ${theme.colors.warning}30`,
            position: 'relative',
            zIndex: 1,
          }}
        >
          <div
            style={{
              fontSize: '32px',
              color: 'white',
              fontWeight: 'bold',
            }}
          >
            🗺️
          </div>
        </div>

        {/* Coming Soon Text */}
        <h3
          style={{
            margin: 0,
            marginBottom: theme.spacing.md,
            color: theme.colors.text.primary,
            fontSize: '24px',
            fontWeight: 700,
            letterSpacing: '-0.5px',
            position: 'relative',
            zIndex: 1,
          }}
        >
          Coming Soon
        </h3>

        {/* Widget Type Info */}
        <p
          style={{
            margin: 0,
            marginBottom: theme.spacing.sm,
            color: theme.colors.text.secondary,
            fontSize: '16px',
            fontWeight: 500,
            position: 'relative',
            zIndex: 1,
          }}
        >
          {widgetType.charAt(0).toUpperCase() + widgetType.slice(1)} Widget
        </p>

        {/* Reason */}
        <p
          style={{
            margin: 0,
            color: theme.colors.text.muted,
            fontSize: '14px',
            lineHeight: 1.5,
            maxWidth: '280px',
            position: 'relative',
            zIndex: 1,
          }}
        >
          {reason}
        </p>

        {/* Progress indicator */}
        <div
          style={{
            marginTop: theme.spacing.lg,
            display: 'flex',
            gap: theme.spacing.xs,
            position: 'relative',
            zIndex: 1,
          }}
        >
          {[0, 1, 2].map((i) => (
            <div
              key={i}
              style={{
                width: '8px',
                height: '8px',
                borderRadius: '50%',
                background: theme.colors.primary,
                opacity: 0.3,
                animation: `pulse 1.5s ease-in-out infinite ${i * 0.2}s`,
              }}
            />
          ))}
        </div>

        {/* CSS Animation */}
        <style>
          {`
            @keyframes pulse {
              0%, 100% { opacity: 0.3; transform: scale(1); }
              50% { opacity: 1; transform: scale(1.1); }
            }
          `}
        </style>
      </div>
    </WidgetContainer>
  );
};
